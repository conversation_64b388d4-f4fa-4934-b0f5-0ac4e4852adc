"""
Seasonality Tab for DataDriven Application

This module provides a tab for seasonality analysis.
Displays percentage-based yearly performance charts.
"""

import pandas as pd
import numpy as np
import yfinance as yf
from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg
import logging
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger(__name__)

# Theme colors for consistency with the application
THEME_COLORS = {
    'background': '#1E1E1E',               # Dark background
    'control_panel': '#2A2A2A',           # Control panel background
    'text': '#E0E0E0',                    # Light gray text
    'grid': '#404040',                    # Grid lines
    'highlight': '#FFC107',               # Amber highlight
    'button_bg': '#3A3A3A',              # Button background
    'button_hover': '#4A4A4A',           # Button hover
    'button_pressed': '#2A2A2A',         # Button pressed
    'button_shadow': '0 4px 6px rgba(0, 122, 204, 0.3)',  # Button shadow
    'bullish': '#4CAF50',                # Material Design Green
    'bearish': '#F44336',                # Material Design Red
}


class SeasonalityTab(QtWidgets.QWidget):
    """
    Tab for seasonality analysis.
    Currently displays a black background as requested.
    """
    
    def __init__(self, parent=None):
        """
        Initialize the seasonality tab.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        self.parent = parent
        
        # Initialize chart colors
        self.chart_colors = {
            'background': THEME_COLORS['background'],
            'text': THEME_COLORS['text'],
            'grid': THEME_COLORS['grid'],
            'bullish': THEME_COLORS['bullish'],
            'bearish': THEME_COLORS['bearish'],
            'axis': THEME_COLORS['text']
        }
        
        # Initialize UI
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface."""
        # Main layout
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Create main horizontal layout with separators at 1/4 and 3/4
        main_horizontal_layout = QtWidgets.QHBoxLayout()
        main_horizontal_layout.setContentsMargins(0, 0, 0, 0)
        main_horizontal_layout.setSpacing(0)

        # Left section (1/4 of the width)
        left_widget = QtWidgets.QWidget()
        left_widget.setStyleSheet(f"background-color: {THEME_COLORS['background']};")
        left_layout = QtWidgets.QVBoxLayout(left_widget)
        left_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(10)

        # Years to load input section
        years_layout = QtWidgets.QHBoxLayout()

        # Years to load label
        years_label = QtWidgets.QLabel("Years to load:")
        years_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-size: 12px;")
        years_layout.addWidget(years_label)

        # Years to load spin box
        self.years_spinbox = QtWidgets.QSpinBox()
        self.years_spinbox.setMinimum(1)
        self.years_spinbox.setMaximum(50)
        self.years_spinbox.setValue(5)  # Default value
        self.years_spinbox.setStyleSheet(f"""
            QSpinBox {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['grid']};
                border-radius: 3px;
                padding: 2px;
                font-size: 12px;
                min-width: 60px;
            }}
            QSpinBox::up-button, QSpinBox::down-button {{
                background-color: {THEME_COLORS['button_bg']};
                border: 1px solid {THEME_COLORS['grid']};
            }}
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
                background-color: {THEME_COLORS['button_hover']};
            }}
        """)
        years_layout.addWidget(self.years_spinbox)

        # Add some spacing
        years_layout.addSpacing(10)

        # Advanced Seasonality Charts button
        self.advanced_charts_button = QtWidgets.QPushButton("Advanced Seasonality Charts")
        self.advanced_charts_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['button_bg']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['grid']};
                border-radius: 5px;
                padding: 6px 12px;
                font-size: 11px;
                font-weight: bold;
                min-height: 16px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['button_hover']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['button_pressed']};
            }}
        """)
        self.advanced_charts_button.clicked.connect(self.on_advanced_charts_clicked)
        years_layout.addWidget(self.advanced_charts_button)

        # Add stretch to push everything to the top
        years_layout.addStretch()

        # Add years layout to left layout
        left_layout.addLayout(years_layout)

        # Load button
        self.load_button = QtWidgets.QPushButton("Load")
        self.load_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['button_bg']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['grid']};
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['button_hover']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['button_pressed']};
            }}
        """)
        self.load_button.clicked.connect(self.on_load_clicked)
        left_layout.addWidget(self.load_button)

        # Add some spacing
        left_layout.addSpacing(20)

        # Three boxes evenly spaced
        # Box 1 - Expected Seasonality Bias (takes up half the space)
        self.box1 = QtWidgets.QFrame()
        self.box1.setFrameStyle(QtWidgets.QFrame.Shape.Box)
        self.box1.setStyleSheet(f"""
            QFrame {{
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['grid']};
                border-radius: 5px;
            }}
        """)
        box1_layout = QtWidgets.QVBoxLayout(self.box1)
        box1_layout.setContentsMargins(8, 8, 8, 8)
        box1_layout.setSpacing(5)

        # Title
        box1_title = QtWidgets.QLabel("Expected Seasonality Bias")
        box1_title.setStyleSheet(f"color: {THEME_COLORS['text']}; font-size: 11px; font-weight: bold;")
        box1_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        box1_layout.addWidget(box1_title)

        # Expected bias Day
        self.bias_day_label = QtWidgets.QLabel("Expected bias Day: -")
        self.bias_day_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-size: 10px;")
        self.bias_day_label.setWordWrap(True)
        box1_layout.addWidget(self.bias_day_label)

        # Expected bias Month
        self.bias_month_label = QtWidgets.QLabel("Expected bias Month: -")
        self.bias_month_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-size: 10px;")
        self.bias_month_label.setWordWrap(True)
        box1_layout.addWidget(self.bias_month_label)

        # Expected bias Quarter
        self.bias_quarter_label = QtWidgets.QLabel("Expected bias Quarter: -")
        self.bias_quarter_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-size: 10px;")
        self.bias_quarter_label.setWordWrap(True)
        box1_layout.addWidget(self.bias_quarter_label)

        box1_layout.addStretch()
        left_layout.addWidget(self.box1, 1)  # 10% of space (1/10)

        # Box 2 - Return per Month (takes up half the space)
        self.box2 = QtWidgets.QFrame()
        self.box2.setFrameStyle(QtWidgets.QFrame.Shape.Box)
        self.box2.setStyleSheet(f"""
            QFrame {{
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['grid']};
                border-radius: 5px;
            }}
        """)
        box2_layout = QtWidgets.QVBoxLayout(self.box2)
        box2_layout.setContentsMargins(8, 8, 8, 8)
        box2_layout.setSpacing(5)

        # Title
        box2_title = QtWidgets.QLabel("Return per Month")
        box2_title.setStyleSheet(f"color: {THEME_COLORS['text']}; font-size: 11px; font-weight: bold;")
        box2_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        box2_layout.addWidget(box2_title)

        # Create table widget
        self.monthly_returns_table = QtWidgets.QTableWidget()
        self.monthly_returns_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['grid']};
                gridline-color: {THEME_COLORS['grid']};
                font-size: 9px;
            }}
            QTableWidget::item {{
                padding: 2px;
                border: none;
            }}
            QHeaderView::section {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['grid']};
                padding: 2px;
                font-size: 9px;
                font-weight: bold;
            }}
            QScrollBar:vertical {{
                background-color: #808080;
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: #606060;
                border-radius: 6px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: #707070;
            }}
            QScrollBar:horizontal {{
                background-color: #808080;
                height: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:horizontal {{
                background-color: #606060;
                border-radius: 6px;
                min-width: 20px;
            }}
            QScrollBar::handle:horizontal:hover {{
                background-color: #707070;
            }}
            QScrollBar::add-line, QScrollBar::sub-line {{
                background: none;
                border: none;
            }}
        """)

        # Set table properties
        self.monthly_returns_table.setAlternatingRowColors(True)
        self.monthly_returns_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectionBehavior.SelectRows)
        self.monthly_returns_table.horizontalHeader().setStretchLastSection(True)
        self.monthly_returns_table.verticalHeader().setVisible(False)

        box2_layout.addWidget(self.monthly_returns_table)
        left_layout.addWidget(self.box2, 9)  # 90% of space (9/10)

        # First separator (vertical line)
        separator1 = QtWidgets.QFrame()
        separator1.setFrameShape(QtWidgets.QFrame.Shape.VLine)
        separator1.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        separator1.setStyleSheet(f"color: {THEME_COLORS['grid']};")
        separator1.setFixedWidth(2)

        # Middle section (1/2 of the width) - Chart area
        middle_widget = QtWidgets.QWidget()
        middle_widget.setStyleSheet(f"background-color: {THEME_COLORS['background']};")
        middle_layout = QtWidgets.QVBoxLayout(middle_widget)
        middle_layout.setContentsMargins(10, 10, 10, 10)
        middle_layout.setSpacing(0)

        # Chart placeholder
        self.chart_widget = pg.PlotWidget()
        self.chart_widget.setBackground(THEME_COLORS['background'])
        self.chart_widget.setLabel('left', 'Price', color=THEME_COLORS['text'])
        self.chart_widget.setLabel('bottom', 'Time', color=THEME_COLORS['text'])
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)

        # Disable pan and zoom to anchor the chart
        self.chart_widget.setMouseEnabled(x=False, y=False)
        self.chart_widget.setMenuEnabled(False)

        # Add crosshair to main chart (initially hidden)
        self.crosshair_v = pg.InfiniteLine(angle=90, movable=False, pen=pg.mkPen(color='white', width=2, style=QtCore.Qt.PenStyle.DashLine))
        self.crosshair_h = pg.InfiniteLine(angle=0, movable=False, pen=pg.mkPen(color='white', width=2, style=QtCore.Qt.PenStyle.DashLine))
        self.crosshair_v.setZValue(1000)  # Ensure crosshair stays on top
        self.crosshair_h.setZValue(1000)  # Ensure crosshair stays on top
        self.chart_widget.addItem(self.crosshair_v, ignoreBounds=True)
        self.chart_widget.addItem(self.crosshair_h, ignoreBounds=True)
        self.crosshair_v.hide()  # Initially hidden
        self.crosshair_h.hide()  # Initially hidden

        # Set initial cursor to normal arrow
        self.chart_widget.setCursor(QtCore.Qt.CursorShape.ArrowCursor)

        # Connect mouse move event for crosshair
        self.chart_widget.scene().sigMouseMoved.connect(self.update_crosshair)

        # Style the chart axes
        self.chart_widget.getAxis('left').setTextPen(THEME_COLORS['text'])
        self.chart_widget.getAxis('bottom').setTextPen(THEME_COLORS['text'])
        self.chart_widget.getAxis('left').setPen(THEME_COLORS['grid'])
        self.chart_widget.getAxis('bottom').setPen(THEME_COLORS['grid'])

        middle_layout.addWidget(self.chart_widget)

        # Second separator (vertical line)
        separator2 = QtWidgets.QFrame()
        separator2.setFrameShape(QtWidgets.QFrame.Shape.VLine)
        separator2.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        separator2.setStyleSheet(f"color: {THEME_COLORS['grid']};")
        separator2.setFixedWidth(2)

        # Right section (1/4 of the width) - Info boxes
        right_widget = QtWidgets.QWidget()
        right_widget.setStyleSheet(f"background-color: {THEME_COLORS['background']};")
        right_layout = QtWidgets.QVBoxLayout(right_widget)
        right_layout.setContentsMargins(10, 10, 10, 10)
        right_layout.setSpacing(10)

        # Top right chart
        self.top_right_chart = pg.PlotWidget()
        self.top_right_chart.setBackground(THEME_COLORS['background'])
        self.top_right_chart.setLabel('left', 'Value', color=THEME_COLORS['text'], size='8pt')
        self.top_right_chart.setLabel('bottom', 'Time', color=THEME_COLORS['text'], size='8pt')
        self.top_right_chart.showGrid(x=True, y=True, alpha=0.2)
        self.top_right_chart.setMinimumHeight(120)

        # Disable pan and zoom to anchor the chart
        self.top_right_chart.setMouseEnabled(x=False, y=False)
        self.top_right_chart.setMenuEnabled(False)

        # Style the top chart axes
        self.top_right_chart.getAxis('left').setTextPen(pg.mkPen(color=THEME_COLORS['text'], width=1))
        self.top_right_chart.getAxis('bottom').setTextPen(pg.mkPen(color=THEME_COLORS['text'], width=1))
        self.top_right_chart.getAxis('left').setPen(pg.mkPen(color=THEME_COLORS['grid'], width=1))
        self.top_right_chart.getAxis('bottom').setPen(pg.mkPen(color=THEME_COLORS['grid'], width=1))

        # Set smaller font for axes
        font = QtGui.QFont()
        font.setPointSize(8)
        self.top_right_chart.getAxis('left').setStyle(tickFont=font)
        self.top_right_chart.getAxis('bottom').setStyle(tickFont=font)

        right_layout.addWidget(self.top_right_chart)

        # Horizontal separator line in the middle
        horizontal_separator = QtWidgets.QFrame()
        horizontal_separator.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        horizontal_separator.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        horizontal_separator.setStyleSheet(f"color: {THEME_COLORS['grid']};")
        horizontal_separator.setFixedHeight(2)
        right_layout.addWidget(horizontal_separator)

        # Bottom right chart
        self.bottom_right_chart = pg.PlotWidget()
        self.bottom_right_chart.setBackground(THEME_COLORS['background'])
        self.bottom_right_chart.setLabel('left', 'Value', color=THEME_COLORS['text'], size='8pt')
        self.bottom_right_chart.setLabel('bottom', 'Time', color=THEME_COLORS['text'], size='8pt')
        self.bottom_right_chart.showGrid(x=True, y=True, alpha=0.2)
        self.bottom_right_chart.setMinimumHeight(120)

        # Disable pan and zoom to anchor the chart
        self.bottom_right_chart.setMouseEnabled(x=False, y=False)
        self.bottom_right_chart.setMenuEnabled(False)

        # Style the bottom chart axes
        self.bottom_right_chart.getAxis('left').setTextPen(pg.mkPen(color=THEME_COLORS['text'], width=1))
        self.bottom_right_chart.getAxis('bottom').setTextPen(pg.mkPen(color=THEME_COLORS['text'], width=1))
        self.bottom_right_chart.getAxis('left').setPen(pg.mkPen(color=THEME_COLORS['grid'], width=1))
        self.bottom_right_chart.getAxis('bottom').setPen(pg.mkPen(color=THEME_COLORS['grid'], width=1))

        # Set smaller font for axes
        font = QtGui.QFont()
        font.setPointSize(8)
        self.bottom_right_chart.getAxis('left').setStyle(tickFont=font)
        self.bottom_right_chart.getAxis('bottom').setStyle(tickFont=font)

        right_layout.addWidget(self.bottom_right_chart)

        # Second horizontal separator line
        horizontal_separator2 = QtWidgets.QFrame()
        horizontal_separator2.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        horizontal_separator2.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        horizontal_separator2.setStyleSheet(f"color: {THEME_COLORS['grid']};")
        horizontal_separator2.setFixedHeight(2)
        right_layout.addWidget(horizontal_separator2)

        # Third chart - Current month across years
        self.current_month_chart = pg.PlotWidget()
        self.current_month_chart.setBackground(THEME_COLORS['background'])
        self.current_month_chart.setLabel('left', 'Value', color=THEME_COLORS['text'], size='8pt')
        self.current_month_chart.setLabel('bottom', 'Day', color=THEME_COLORS['text'], size='8pt')
        self.current_month_chart.showGrid(x=True, y=True, alpha=0.2)
        self.current_month_chart.setMinimumHeight(120)

        # Disable pan and zoom to anchor the chart
        self.current_month_chart.setMouseEnabled(x=False, y=False)
        self.current_month_chart.setMenuEnabled(False)

        # Style the current month chart axes
        self.current_month_chart.getAxis('left').setTextPen(pg.mkPen(color=THEME_COLORS['text'], width=1))
        self.current_month_chart.getAxis('bottom').setTextPen(pg.mkPen(color=THEME_COLORS['text'], width=1))
        self.current_month_chart.getAxis('left').setPen(pg.mkPen(color=THEME_COLORS['grid'], width=1))
        self.current_month_chart.getAxis('bottom').setPen(pg.mkPen(color=THEME_COLORS['grid'], width=1))

        # Set smaller font for axes
        font = QtGui.QFont()
        font.setPointSize(8)
        self.current_month_chart.getAxis('left').setStyle(tickFont=font)
        self.current_month_chart.getAxis('bottom').setStyle(tickFont=font)

        right_layout.addWidget(self.current_month_chart)

        # Add widgets to horizontal layout with proper proportions
        main_horizontal_layout.addWidget(left_widget, 1)      # 1/4 width
        main_horizontal_layout.addWidget(separator1)          # Separator
        main_horizontal_layout.addWidget(middle_widget, 2)    # 1/2 width
        main_horizontal_layout.addWidget(separator2)          # Separator
        main_horizontal_layout.addWidget(right_widget, 1)     # 1/4 width

        # Create a container widget for the horizontal layout
        container_widget = QtWidgets.QWidget()
        container_widget.setLayout(main_horizontal_layout)
        container_widget.setStyleSheet(f"background-color: {THEME_COLORS['background']};")

        # Add the container to the main layout
        main_layout.addWidget(container_widget)

        # Set the main widget background to black as well
        self.setStyleSheet(f"background-color: {THEME_COLORS['background']};")

        # Connect years spinbox to update method
        self.years_spinbox.valueChanged.connect(self.on_years_changed)

    def update_crosshair(self, pos):
        """
        Update crosshair position based on mouse movement.
        Hide mouse cursor when over chart, show crosshair.
        Show mouse cursor when outside chart, hide crosshair.

        Args:
            pos: Mouse position from the scene
        """
        try:
            # Check if the mouse is within the chart widget's plot area
            if self.chart_widget.sceneBoundingRect().contains(pos):
                # Convert scene coordinates to data coordinates
                mouse_point = self.chart_widget.plotItem.vb.mapSceneToView(pos)

                # Update crosshair lines
                self.crosshair_v.setPos(mouse_point.x())
                self.crosshair_h.setPos(mouse_point.y())

                # Show crosshair lines and hide mouse cursor
                self.crosshair_v.show()
                self.crosshair_h.show()
                self.chart_widget.setCursor(QtCore.Qt.CursorShape.BlankCursor)
            else:
                # Hide crosshair when mouse is outside chart area and show mouse cursor
                self.crosshair_v.hide()
                self.crosshair_h.hide()
                self.chart_widget.setCursor(QtCore.Qt.CursorShape.ArrowCursor)

        except Exception as e:
            # Hide crosshair on any error and show mouse cursor
            self.crosshair_v.hide()
            self.crosshair_h.hide()
            self.chart_widget.setCursor(QtCore.Qt.CursorShape.ArrowCursor)

    def on_advanced_charts_clicked(self):
        """
        Handle the Advanced Seasonality Charts button click.
        Creates 5 lines showing specific years dynamically calculated from current year:
        - Line 1: 2 years back from current year
        - Line 2: 5 years back from current year
        - Line 3: 10 years back from current year
        - Line 4: 15 years back from current year
        - Line 5: 20 years back from current year
        """
        logger.info("Advanced Seasonality Charts button clicked")

        # Get symbol from universal controls
        symbol = self.get_symbol_from_universal_controls()
        if not symbol:
            logger.warning("No symbol available from universal controls")
            return

        # Clear the main chart
        self.chart_widget.clear()

        # Show loading message
        self.show_loading_message(f"Loading advanced seasonality data for {symbol}...")

        # Load and process advanced seasonality data
        try:
            self.load_and_display_advanced_seasonality(symbol)
        except Exception as e:
            logger.error(f"Error loading advanced seasonality data: {str(e)}")
            self.show_error_message(f"Error loading advanced data: {str(e)}")

    def populate_advanced_additional_charts(self, data, symbol, years_to_show):
        """
        Populate the top and bottom right charts with advanced analysis using specific years.

        Args:
            data: Historical price data
            symbol: Stock symbol for labeling
            years_to_show: List of specific years to analyze
        """
        # Top chart: Monthly average performance for specific years
        self.create_advanced_monthly_average_chart(data, symbol, years_to_show)

        # Bottom chart: Volatility by month for specific years
        self.create_advanced_monthly_volatility_chart(data, symbol, years_to_show)

        # Third chart: Current month across specific years
        self.create_advanced_current_month_chart(data, symbol, years_to_show)

    def on_load_clicked(self):
        """
        Handle the Load button click.
        Load the selected years of data and display as percentage chart.
        """
        years = self.get_years_to_load()
        logger.info(f"Load button clicked with {years} years to load")

        # Get symbol from universal controls
        symbol = self.get_symbol_from_universal_controls()
        if not symbol:
            logger.warning("No symbol available from universal controls")
            return

        # Clear the chart
        self.chart_widget.clear()

        # Show loading message
        self.show_loading_message(f"Loading {years} years of {symbol} data...")

        # Load and process data
        try:
            self.load_and_display_seasonality_data(symbol, years)
        except Exception as e:
            logger.error(f"Error loading seasonality data: {str(e)}")
            self.show_error_message(f"Error loading data: {str(e)}")

    def get_symbol_from_universal_controls(self):
        """
        Get the current symbol from universal controls.

        Returns:
            str: The symbol or None if not available
        """
        try:
            # Navigate up to find the main window with universal controls
            parent = self.parent
            while parent and not hasattr(parent, 'universal_controls'):
                parent = parent.parent() if hasattr(parent, 'parent') else getattr(parent, 'parent', None)

            if parent and hasattr(parent, 'universal_controls'):
                symbol = parent.universal_controls.symbol_input.text().strip().upper()
                return symbol if symbol else None

            # Alternative: try to get from main window directly
            from PyQt6.QtWidgets import QApplication
            for widget in QApplication.topLevelWidgets():
                if hasattr(widget, 'universal_controls'):
                    symbol = widget.universal_controls.symbol_input.text().strip().upper()
                    return symbol if symbol else None

        except Exception as e:
            logger.error(f"Error getting symbol from universal controls: {str(e)}")

        return None

    def load_and_display_seasonality_data(self, symbol, years):
        """
        Load historical data and display as percentage-based yearly charts.

        Args:
            symbol: The stock symbol to load
            years: Number of years to load
        """
        current_year = datetime.now().year

        # Calculate date range to get full years
        # Start from January 1st of (current_year - years + 1)
        start_year = current_year - years + 1
        start_date = datetime(start_year, 1, 1)
        end_date = datetime.now()

        # Fetch data using yfinance
        ticker = yf.Ticker(symbol)
        data = ticker.history(start=start_date, end=end_date, interval='1d')

        if data.empty:
            self.show_error_message(f"No data available for {symbol}")
            return

        # Clear all charts
        self.chart_widget.clear()
        self.top_right_chart.clear()
        self.bottom_right_chart.clear()
        self.current_month_chart.clear()

        # Re-add crosshair after clearing main chart
        self.chart_widget.addItem(self.crosshair_v, ignoreBounds=True)
        self.chart_widget.addItem(self.crosshair_h, ignoreBounds=True)
        self.crosshair_v.hide()  # Initially hidden
        self.crosshair_h.hide()  # Initially hidden

        # Process data by year and create percentage charts
        self.create_yearly_percentage_charts(data, symbol)

        # Populate the additional charts
        self.populate_additional_charts(data, symbol)

        # Calculate and update seasonality bias
        self.calculate_seasonality_bias(data)

        # Populate monthly returns table
        self.populate_monthly_returns_table(data)

    def load_and_display_advanced_seasonality(self, symbol):
        """
        Load and display advanced seasonality charts with 5 individual year lines.

        Args:
            symbol: The stock symbol to load
        """
        current_year = datetime.now().year

        # Define the 5 dynamic years based on current year
        years_to_show = [
            {"name": f"{current_year - 2}", "year": current_year - 2, "color": "#FF6B6B"},  # 2 years back
            {"name": f"{current_year - 5}", "year": current_year - 5, "color": "#4ECDC4"},  # 5 years back
            {"name": f"{current_year - 10}", "year": current_year - 10, "color": "#45B7D1"}, # 10 years back
            {"name": f"{current_year - 15}", "year": current_year - 15, "color": "#96CEB4"}, # 15 years back
            {"name": f"{current_year - 20}", "year": current_year - 20, "color": "#FFEAA7"}  # 20 years back
        ]

        # Load data for the longest period (20 years)
        start_date = datetime(current_year - 20, 1, 1)
        end_date = datetime(current_year - 1, 12, 31)  # Exclude current year

        # Fetch data using yfinance
        ticker = yf.Ticker(symbol)
        data = ticker.history(start=start_date, end=end_date, interval='1d')

        if data.empty:
            self.show_error_message(f"No data available for {symbol}")
            return

        # Clear all charts
        self.chart_widget.clear()
        self.top_right_chart.clear()
        self.bottom_right_chart.clear()
        self.current_month_chart.clear()

        # Re-add crosshair after clearing main chart
        self.chart_widget.addItem(self.crosshair_v, ignoreBounds=True)
        self.chart_widget.addItem(self.crosshair_h, ignoreBounds=True)
        self.crosshair_v.hide()  # Initially hidden
        self.crosshair_h.hide()  # Initially hidden

        # Create individual year lines for main chart
        self.create_advanced_seasonality_lines(data, symbol, years_to_show)

        # Populate the additional charts with advanced data (using same calculation methods)
        self.populate_additional_charts(data, symbol)

        # Update current month chart to show only specific years
        self.populate_advanced_additional_charts(data, symbol, years_to_show)

        # Calculate and update seasonality bias
        self.calculate_seasonality_bias(data)

        # Populate monthly returns table
        self.populate_monthly_returns_table(data)

    def create_yearly_percentage_charts(self, data, symbol):
        """
        Create percentage-based charts for each year, starting at 0%.

        Args:
            data: Historical price data
            symbol: Stock symbol for labeling
        """
        current_year = datetime.now().year

        # Group data by year, ensuring we only include complete years (except current year)
        yearly_data = {}
        for year in range(data.index[0].year, data.index[-1].year + 1):
            year_data = data[data.index.year == year]

            if len(year_data) == 0:
                continue

            # For past years, only include if we have data from January
            # For current year, include whatever data we have
            if year < current_year:
                # Check if we have data from early in the year (within first 2 months)
                first_date = year_data.index[0]
                if first_date.month <= 2:  # Data starts in Jan or Feb
                    yearly_data[year] = year_data
            else:
                # Current year - include all available data
                yearly_data[year] = year_data

        # Create color palette for different years
        colors = [
            '#FF6B6B',  # Red
            '#4ECDC4',  # Teal
            '#45B7D1',  # Blue
            '#96CEB4',  # Green
            '#FFEAA7',  # Yellow
            '#DDA0DD',  # Plum
            '#98D8C8',  # Mint
            '#F7DC6F',  # Light Yellow
            '#BB8FCE',  # Light Purple
            '#85C1E9',  # Light Blue
        ]

        # Plot each year as a percentage chart (excluding current year)
        current_year = datetime.now().year
        for i, (year, year_data) in enumerate(yearly_data.items()):
            if len(year_data) < 2:  # Need at least 2 data points
                continue

            # Skip current year for mid section chart
            if year == current_year:
                continue

            # Calculate percentage change from start of year
            start_price = year_data['Close'].iloc[0]
            percentage_data = ((year_data['Close'] - start_price) / start_price) * 100

            # Create day-of-year x-axis (1-365)
            day_of_year = year_data.index.dayofyear

            # Plot the line
            color = colors[i % len(colors)]
            pen = pg.mkPen(color=color, width=2)
            self.chart_widget.plot(
                day_of_year,
                percentage_data,
                pen=pen,
                name=f"{year}"
            )

        # Configure chart
        self.chart_widget.setLabel('left', 'Percentage Change (%)', color=THEME_COLORS['text'])
        self.chart_widget.setLabel('bottom', 'Day of Year', color=THEME_COLORS['text'])
        self.chart_widget.setTitle(f'{symbol} - Yearly Seasonality (% Change)', color=THEME_COLORS['text'])

        # Add legend
        self.chart_widget.addLegend()

        # Add horizontal line at 0%
        zero_line = pg.InfiniteLine(pos=0, angle=0, pen=pg.mkPen(color=THEME_COLORS['grid'], width=1, style=QtCore.Qt.PenStyle.DashLine))
        self.chart_widget.addItem(zero_line)

        # Add vertical line for current day of year
        current_date = datetime.now()
        current_day_of_year = current_date.timetuple().tm_yday
        current_day_line = pg.InfiniteLine(
            pos=current_day_of_year,
            angle=90,
            pen=pg.mkPen(color='red', width=1, style=QtCore.Qt.PenStyle.SolidLine)
        )
        self.chart_widget.addItem(current_day_line)

        # Set grid
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)

    def show_loading_message(self, message):
        """Show a loading message in the chart area."""
        self.chart_widget.clear()
        text_item = pg.TextItem(
            message,
            color=THEME_COLORS['text'],
            anchor=(0.5, 0.5)
        )
        # Position in center of chart
        text_item.setPos(180, 0)  # Roughly center of year (day 180)
        self.chart_widget.addItem(text_item)

    def show_error_message(self, message):
        """Show an error message in the chart area."""
        self.chart_widget.clear()
        text_item = pg.TextItem(
            message,
            color='#FF6B6B',  # Red color for errors
            anchor=(0.5, 0.5)
        )
        text_item.setPos(180, 0)
        self.chart_widget.addItem(text_item)

    def populate_additional_charts(self, data, symbol):
        """
        Populate the top and bottom right charts with additional analysis.

        Args:
            data: Historical price data
            symbol: Stock symbol for labeling
        """
        # Top chart: Monthly average performance
        self.create_monthly_average_chart(data, symbol)

        # Bottom chart: Volatility by month
        self.create_monthly_volatility_chart(data, symbol)

        # Third chart: Current month across years
        self.create_current_month_chart(data, symbol)

    def populate_advanced_additional_charts(self, data, symbol, years_to_show):
        """
        Populate the additional charts with advanced analysis using the same calculation methods.
        This method is called for advanced seasonality charts but uses the same calculations
        as the normal mode, just with different data filtering.

        Args:
            data: Historical price data
            symbol: Stock symbol for labeling
            years_to_show: List of specific years to analyze
        """
        # For advanced mode, we still use the same calculation methods
        # but we can add specific year filtering to the current month chart
        self.create_advanced_current_month_chart(data, symbol, years_to_show)

    def create_monthly_average_chart(self, data, symbol):
        """
        Create a chart showing average monthly performance.

        Args:
            data: Historical price data
            symbol: Stock symbol for labeling
        """
        try:
            # Calculate monthly returns
            monthly_data = data.resample('M')['Close'].last()
            monthly_returns = monthly_data.pct_change().dropna() * 100

            # Group by month and calculate average
            monthly_avg = monthly_returns.groupby(monthly_returns.index.month).mean()

            # Create month labels
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

            # Plot bar chart
            x_pos = list(range(1, 13))
            y_values = [monthly_avg.get(i, 0) for i in x_pos]

            # Create bar graph
            bargraph = pg.BarGraphItem(x=x_pos, height=y_values, width=0.8,
                                     brush=pg.mkBrush(color=THEME_COLORS['bullish']))
            self.top_right_chart.addItem(bargraph)

            # Set labels and title
            self.top_right_chart.setLabel('left', 'Avg Return (%)', color=THEME_COLORS['text'], size='8pt')
            self.top_right_chart.setLabel('bottom', 'Month', color=THEME_COLORS['text'], size='8pt')
            self.top_right_chart.setTitle(f'{symbol} - Monthly Avg Returns', color=THEME_COLORS['text'], size='9pt')

            # Set x-axis ticks
            ax = self.top_right_chart.getAxis('bottom')
            ax.setTicks([[(i+1, months[i]) for i in range(12)]])

        except Exception as e:
            logger.error(f"Error creating monthly average chart: {str(e)}")

    def create_monthly_volatility_chart(self, data, symbol):
        """
        Create a chart showing volatility by month.

        Args:
            data: Historical price data
            symbol: Stock symbol for labeling
        """
        try:
            # Calculate daily returns
            daily_returns = data['Close'].pct_change().dropna()

            # Calculate monthly volatility (standard deviation)
            monthly_vol = daily_returns.groupby(daily_returns.index.month).std() * np.sqrt(252) * 100  # Annualized

            # Create month labels
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

            # Plot line chart (include all months)
            x_pos = list(range(1, 13))
            y_values = [monthly_vol.get(i, 0) for i in x_pos]

            # Create line plot
            pen = pg.mkPen(color=THEME_COLORS['bearish'], width=2)
            self.bottom_right_chart.plot(x_pos, y_values, pen=pen, symbol='o', symbolBrush=THEME_COLORS['bearish'])

            # Set labels and title
            self.bottom_right_chart.setLabel('left', 'Volatility (%)', color=THEME_COLORS['text'], size='8pt')
            self.bottom_right_chart.setLabel('bottom', 'Month', color=THEME_COLORS['text'], size='8pt')
            self.bottom_right_chart.setTitle(f'{symbol} - Monthly Volatility', color=THEME_COLORS['text'], size='9pt')

            # Set x-axis ticks (include all months)
            ax = self.bottom_right_chart.getAxis('bottom')
            ax.setTicks([[(i+1, months[i]) for i in range(12)]])

        except Exception as e:
            logger.error(f"Error creating monthly volatility chart: {str(e)}")

    def create_current_month_chart(self, data, symbol):
        """
        Create a chart showing the current month across all years with current day marker.

        Args:
            data: Historical price data
            symbol: Stock symbol for labeling
        """
        try:
            current_date = datetime.now()
            current_month = current_date.month
            current_day = current_date.day

            # Get month name
            month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                          'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            current_month_name = month_names[current_month - 1]

            # Group data by year and filter for current month
            yearly_data = {}
            for year in range(data.index[0].year, data.index[-1].year + 1):
                year_data = data[data.index.year == year]
                if len(year_data) > 0:
                    # Filter for current month
                    month_data = year_data[year_data.index.month == current_month]
                    if len(month_data) > 0:
                        yearly_data[year] = month_data

            # Create color palette for different years
            colors = [
                '#FF6B6B',  # Red
                '#4ECDC4',  # Teal
                '#45B7D1',  # Blue
                '#96CEB4',  # Green
                '#FFEAA7',  # Yellow
                '#DDA0DD',  # Plum
                '#98D8C8',  # Mint
                '#F7DC6F',  # Light Yellow
                '#BB8FCE',  # Light Purple
                '#85C1E9',  # Light Blue
            ]

            # Plot each year's current month data as percentage change
            for i, (year, month_data) in enumerate(yearly_data.items()):
                if len(month_data) < 2:  # Need at least 2 data points
                    continue

                # Calculate percentage change from start of month
                start_price = month_data['Close'].iloc[0]
                percentage_data = ((month_data['Close'] - start_price) / start_price) * 100

                # Create day-of-month x-axis
                day_of_month = month_data.index.day

                # Plot the line
                color = colors[i % len(colors)]
                pen = pg.mkPen(color=color, width=2)
                self.current_month_chart.plot(
                    day_of_month,
                    percentage_data,
                    pen=pen,
                    name=f"{year}"
                )

            # Add vertical line for current day (1pt red solid line, no label)
            current_day_line = pg.InfiniteLine(
                pos=current_day,
                angle=90,
                pen=pg.mkPen(color='red', width=1, style=QtCore.Qt.PenStyle.SolidLine)
            )
            self.current_month_chart.addItem(current_day_line)

            # Configure chart
            self.current_month_chart.setLabel('left', 'Change (%)', color=THEME_COLORS['text'], size='8pt')
            self.current_month_chart.setLabel('bottom', f'{current_month_name} Day', color=THEME_COLORS['text'], size='8pt')
            self.current_month_chart.setTitle(f'{symbol} - {current_month_name} Performance', color=THEME_COLORS['text'], size='9pt')

            # Add legend if there are multiple years
            if len(yearly_data) > 1:
                self.current_month_chart.addLegend(size=(60, 40))

            # Add horizontal line at 0%
            zero_line = pg.InfiniteLine(pos=0, angle=0, pen=pg.mkPen(color=THEME_COLORS['grid'], width=1, style=QtCore.Qt.PenStyle.DashLine))
            self.current_month_chart.addItem(zero_line)

        except Exception as e:
            logger.error(f"Error creating current month chart: {str(e)}")



    def create_advanced_seasonality_lines(self, data, symbol, years_to_show):
        """
        Create the 5 individual year seasonality lines.

        Args:
            data: Historical price data
            symbol: Stock symbol for labeling
            years_to_show: List of specific years to display
        """
        try:
            # For each specific year, create a seasonality line
            for year_info in years_to_show:
                target_year = year_info["year"]

                # Filter data for this specific year
                year_data = data[data.index.year == target_year]

                if len(year_data) < 2:  # Need at least 2 data points
                    continue

                # Calculate percentage change from start of year
                start_price = year_data['Close'].iloc[0]
                percentage_data = ((year_data['Close'] - start_price) / start_price) * 100

                # Create day-of-year x-axis (1-365)
                day_of_year = year_data.index.dayofyear

                # Plot the line for this specific year
                pen = pg.mkPen(color=year_info["color"], width=3)
                self.chart_widget.plot(
                    day_of_year,
                    percentage_data,
                    pen=pen,
                    name=year_info["name"]
                )

            # Add vertical line for current day of year
            current_date = datetime.now()
            current_day_of_year = current_date.timetuple().tm_yday
            current_day_line = pg.InfiniteLine(
                pos=current_day_of_year,
                angle=90,
                pen=pg.mkPen(color='red', width=1, style=QtCore.Qt.PenStyle.SolidLine)
            )
            self.chart_widget.addItem(current_day_line)

            # Add horizontal line at 0%
            zero_line = pg.InfiniteLine(pos=0, angle=0, pen=pg.mkPen(color=THEME_COLORS['grid'], width=1, style=QtCore.Qt.PenStyle.DashLine))
            self.chart_widget.addItem(zero_line)

            # Configure chart
            self.chart_widget.setLabel('left', 'Percentage Change (%)', color=THEME_COLORS['text'])
            self.chart_widget.setLabel('bottom', 'Day of Year', color=THEME_COLORS['text'])
            self.chart_widget.setTitle(f'{symbol} - Advanced Seasonality (2, 5, 10, 15, 20 Year Lookback)', color=THEME_COLORS['text'])

            # Add legend
            self.chart_widget.addLegend()

            # Set grid
            self.chart_widget.showGrid(x=True, y=True, alpha=0.3)

        except Exception as e:
            logger.error(f"Error creating advanced seasonality lines: {str(e)}")

    def calculate_average_seasonality(self, data):
        """
        Calculate average seasonality pattern from multi-year data.

        Args:
            data: Historical price data for multiple years

        Returns:
            pandas.Series: Average percentage change by day of year
        """
        try:
            # Group data by year and calculate yearly percentage changes
            yearly_seasonality = {}

            for year in data.index.year.unique():
                year_data = data[data.index.year == year]
                if len(year_data) < 2:
                    continue

                # Calculate percentage change from start of year
                start_price = year_data['Close'].iloc[0]
                percentage_data = ((year_data['Close'] - start_price) / start_price) * 100

                # Store by day of year
                for date, pct_change in percentage_data.items():
                    day_of_year = date.timetuple().tm_yday
                    if day_of_year not in yearly_seasonality:
                        yearly_seasonality[day_of_year] = []
                    yearly_seasonality[day_of_year].append(pct_change)

            # Calculate average for each day of year
            avg_seasonality = {}
            for day_of_year, values in yearly_seasonality.items():
                if len(values) > 0:
                    avg_seasonality[day_of_year] = np.mean(values)

            # Convert to pandas Series and sort by day of year
            if avg_seasonality:
                return pd.Series(avg_seasonality).sort_index()
            else:
                return None

        except Exception as e:
            logger.error(f"Error calculating average seasonality: {str(e)}")
            return None

    def create_advanced_monthly_average_chart(self, data, symbol, years_to_show):
        """
        Create a chart showing monthly performance for specific years.

        Args:
            data: Historical price data
            symbol: Stock symbol for labeling
            years_to_show: List of specific years to analyze
        """
        try:
            # Create month labels
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

            # For each specific year, calculate monthly returns and plot
            for year_info in years_to_show:
                target_year = year_info["year"]
                year_data = data[data.index.year == target_year]

                if len(year_data) == 0:
                    continue

                # Calculate monthly returns for this year
                monthly_data = year_data.resample('M')['Close'].last()
                monthly_returns = monthly_data.pct_change().dropna() * 100

                # Create x positions and y values
                x_pos = []
                y_values = []
                for month in range(1, 13):
                    month_return = monthly_returns[monthly_returns.index.month == month]
                    if len(month_return) > 0:
                        x_pos.append(month)
                        y_values.append(month_return.iloc[0])

                if len(x_pos) > 0:
                    # Plot line for this year
                    pen = pg.mkPen(color=year_info["color"], width=2)
                    self.top_right_chart.plot(x_pos, y_values, pen=pen,
                                            symbol='o', symbolBrush=year_info["color"],
                                            name=year_info["name"])

            # Set labels and title
            self.top_right_chart.setLabel('left', 'Monthly Return (%)', color=THEME_COLORS['text'], size='8pt')
            self.top_right_chart.setLabel('bottom', 'Month', color=THEME_COLORS['text'], size='8pt')
            self.top_right_chart.setTitle(f'{symbol} - Monthly Returns (Specific Years)', color=THEME_COLORS['text'], size='9pt')

            # Set x-axis ticks
            ax = self.top_right_chart.getAxis('bottom')
            ax.setTicks([[(i+1, months[i]) for i in range(12)]])

            # Add legend
            self.top_right_chart.addLegend(size=(80, 60))

        except Exception as e:
            logger.error(f"Error creating advanced monthly average chart: {str(e)}")

    def create_advanced_monthly_volatility_chart(self, data, symbol, years_to_show):
        """
        Create a chart showing volatility by month for specific years.

        Args:
            data: Historical price data
            symbol: Stock symbol for labeling
            years_to_show: List of specific years to analyze
        """
        try:
            # Create month labels
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

            # For each specific year, calculate monthly volatility and plot
            for year_info in years_to_show:
                target_year = year_info["year"]
                year_data = data[data.index.year == target_year]

                if len(year_data) == 0:
                    continue

                # Calculate daily returns for this year
                daily_returns = year_data['Close'].pct_change().dropna()

                # Calculate monthly volatility (standard deviation)
                monthly_vol = daily_returns.groupby(daily_returns.index.month).std() * np.sqrt(21) * 100  # Monthly annualized

                # Create x positions and y values
                x_pos = list(range(1, 13))
                y_values = [monthly_vol.get(i, 0) for i in x_pos]

                # Plot line for this year
                pen = pg.mkPen(color=year_info["color"], width=2)
                self.bottom_right_chart.plot(x_pos, y_values, pen=pen,
                                           symbol='o', symbolBrush=year_info["color"],
                                           name=year_info["name"])

            # Set labels and title
            self.bottom_right_chart.setLabel('left', 'Volatility (%)', color=THEME_COLORS['text'], size='8pt')
            self.bottom_right_chart.setLabel('bottom', 'Month', color=THEME_COLORS['text'], size='8pt')
            self.bottom_right_chart.setTitle(f'{symbol} - Monthly Volatility (Specific Years)', color=THEME_COLORS['text'], size='9pt')

            # Set x-axis ticks
            ax = self.bottom_right_chart.getAxis('bottom')
            ax.setTicks([[(i+1, months[i]) for i in range(12)]])

            # Add legend
            self.bottom_right_chart.addLegend(size=(80, 60))

        except Exception as e:
            logger.error(f"Error creating advanced monthly volatility chart: {str(e)}")

    def create_advanced_current_month_chart(self, data, symbol, years_to_show):
        """
        Create a chart showing the current month across specific years with current day marker.
        Uses the same calculation method as normal mode but filters for specific years.

        Args:
            data: Historical price data
            symbol: Stock symbol for labeling
            years_to_show: List of specific years to analyze
        """
        try:
            current_date = datetime.now()
            current_month = current_date.month
            current_day = current_date.day

            # Get month name
            month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                          'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            current_month_name = month_names[current_month - 1]

            # Clear the current month chart first
            self.current_month_chart.clear()

            # Extract the specific years we want to show
            target_years = [year_info["year"] for year_info in years_to_show]

            # Group data by year and filter for current month (same as normal mode)
            yearly_data = {}
            for year in range(data.index[0].year, data.index[-1].year + 1):
                # Only include the specific years we want to show
                if year not in target_years:
                    continue

                year_data = data[data.index.year == year]
                if len(year_data) > 0:
                    # Filter for current month
                    month_data = year_data[year_data.index.month == current_month]
                    if len(month_data) > 0:
                        yearly_data[year] = month_data

            # Create color mapping for the specific years
            year_color_map = {year_info["year"]: year_info["color"] for year_info in years_to_show}

            # Plot each year's current month data as percentage change (same calculation as normal mode)
            for year, month_data in yearly_data.items():
                if len(month_data) < 2:  # Need at least 2 data points
                    continue

                # Calculate percentage change from start of month (same as normal mode)
                start_price = month_data['Close'].iloc[0]
                percentage_data = ((month_data['Close'] - start_price) / start_price) * 100

                # Create day-of-month x-axis (same as normal mode)
                day_of_month = month_data.index.day

                # Plot the line using the specific color for this year
                color = year_color_map.get(year, '#FFFFFF')  # Default to white if not found
                pen = pg.mkPen(color=color, width=2)
                self.current_month_chart.plot(
                    day_of_month,
                    percentage_data,
                    pen=pen,
                    name=f"{year}"
                )

            # Add vertical line for current day (1pt red solid line, no label) - same as normal mode
            current_day_line = pg.InfiniteLine(
                pos=current_day,
                angle=90,
                pen=pg.mkPen(color='red', width=1, style=QtCore.Qt.PenStyle.SolidLine)
            )
            self.current_month_chart.addItem(current_day_line)

            # Configure chart (same as normal mode)
            self.current_month_chart.setLabel('left', 'Change (%)', color=THEME_COLORS['text'], size='8pt')
            self.current_month_chart.setLabel('bottom', f'{current_month_name} Day', color=THEME_COLORS['text'], size='8pt')
            self.current_month_chart.setTitle(f'{symbol} - {current_month_name} (2, 5, 10, 15, 20 Yr Back)', color=THEME_COLORS['text'], size='9pt')

            # Add legend if there are multiple years (same as normal mode)
            if len(yearly_data) > 1:
                self.current_month_chart.addLegend(size=(80, 60))

            # Add horizontal line at 0% (same as normal mode)
            zero_line = pg.InfiniteLine(pos=0, angle=0, pen=pg.mkPen(color=THEME_COLORS['grid'], width=1, style=QtCore.Qt.PenStyle.DashLine))
            self.current_month_chart.addItem(zero_line)

        except Exception as e:
            logger.error(f"Error creating advanced current month chart: {str(e)}")

    def calculate_seasonality_bias(self, data):
        """
        Calculate expected seasonality bias for day, month, and quarter.

        Args:
            data: Historical price data
        """
        try:
            current_date = datetime.now()
            current_day = current_date.day
            current_month = current_date.month
            current_quarter = (current_month - 1) // 3 + 1

            # Get years from the loaded data
            years = sorted(data.index.year.unique())
            if len(years) < 2:
                self.update_bias_labels("Insufficient data", "Insufficient data", "Insufficient data")
                return

            # Calculate daily bias
            daily_bias = self.calculate_daily_bias(data, years, current_day, current_month)

            # Calculate monthly bias
            monthly_bias = self.calculate_monthly_bias(data, years, current_month)

            # Calculate quarterly bias
            quarterly_bias = self.calculate_quarterly_bias(data, years, current_quarter)

            # Update the labels
            self.update_bias_labels(daily_bias, monthly_bias, quarterly_bias)

        except Exception as e:
            logger.error(f"Error calculating seasonality bias: {str(e)}")
            self.update_bias_labels("Error", "Error", "Error")

    def calculate_daily_bias(self, data, years, current_day, current_month):
        """
        Calculate daily bias by comparing current day with previous day across years.

        Args:
            data: Historical price data
            years: List of years to analyze
            current_day: Current day of month
            current_month: Current month

        Returns:
            str: Bias result (Bullish, Bearish, Neutral)
        """
        try:
            bullish_count = 0
            total_count = 0

            for year in years:
                year_data = data[data.index.year == year]

                # Get current month data for this year
                month_data = year_data[(year_data.index.month == current_month)]

                if len(month_data) < 2:
                    continue

                # Find current day and previous day
                current_day_data = month_data[month_data.index.day == current_day]
                prev_day_data = month_data[month_data.index.day == (current_day - 1)]

                if len(current_day_data) > 0 and len(prev_day_data) > 0:
                    current_price = current_day_data['Close'].iloc[0]
                    prev_price = prev_day_data['Close'].iloc[0]

                    if current_price > prev_price:
                        bullish_count += 1
                    total_count += 1

            return self.determine_bias(bullish_count, total_count)

        except Exception as e:
            logger.error(f"Error calculating daily bias: {str(e)}")
            return "Error"

    def calculate_monthly_bias(self, data, years, current_month):
        """
        Calculate monthly bias by comparing current month with previous month across years.

        Args:
            data: Historical price data
            years: List of years to analyze
            current_month: Current month

        Returns:
            str: Bias result (Bullish, Bearish, Neutral)
        """
        try:
            bullish_count = 0
            total_count = 0

            for year in years:
                year_data = data[data.index.year == year]

                # Get current month and previous month data
                current_month_data = year_data[year_data.index.month == current_month]
                prev_month = current_month - 1 if current_month > 1 else 12
                prev_year = year if current_month > 1 else year - 1

                prev_month_data = data[(data.index.year == prev_year) & (data.index.month == prev_month)]

                if len(current_month_data) > 0 and len(prev_month_data) > 0:
                    current_month_close = current_month_data['Close'].iloc[-1]  # Last day of current month
                    prev_month_close = prev_month_data['Close'].iloc[-1]  # Last day of previous month

                    if current_month_close > prev_month_close:
                        bullish_count += 1
                    total_count += 1

            return self.determine_bias(bullish_count, total_count)

        except Exception as e:
            logger.error(f"Error calculating monthly bias: {str(e)}")
            return "Error"

    def calculate_quarterly_bias(self, data, years, current_quarter):
        """
        Calculate quarterly bias by comparing current quarter with previous quarter across years.

        Args:
            data: Historical price data
            years: List of years to analyze
            current_quarter: Current quarter (1-4)

        Returns:
            str: Bias result (Bullish, Bearish, Neutral)
        """
        try:
            bullish_count = 0
            total_count = 0

            for year in years:
                # Get current quarter months
                current_q_months = [(current_quarter - 1) * 3 + i for i in range(1, 4)]
                current_q_data = data[(data.index.year == year) & (data.index.month.isin(current_q_months))]

                # Get previous quarter months
                prev_quarter = current_quarter - 1 if current_quarter > 1 else 4
                prev_year = year if current_quarter > 1 else year - 1
                prev_q_months = [(prev_quarter - 1) * 3 + i for i in range(1, 4)]
                prev_q_data = data[(data.index.year == prev_year) & (data.index.month.isin(prev_q_months))]

                if len(current_q_data) > 0 and len(prev_q_data) > 0:
                    current_q_close = current_q_data['Close'].iloc[-1]  # Last day of current quarter
                    prev_q_close = prev_q_data['Close'].iloc[-1]  # Last day of previous quarter

                    if current_q_close > prev_q_close:
                        bullish_count += 1
                    total_count += 1

            return self.determine_bias(bullish_count, total_count)

        except Exception as e:
            logger.error(f"Error calculating quarterly bias: {str(e)}")
            return "Error"

    def determine_bias(self, bullish_count, total_count):
        """
        Determine bias based on bullish count and total count.

        Args:
            bullish_count: Number of bullish occurrences
            total_count: Total number of occurrences

        Returns:
            str: Bias result (Bullish, Bearish, Neutral)
        """
        if total_count == 0:
            return "No data"

        ratio = bullish_count / total_count

        if ratio >= 0.6:  # 3/5 or better
            return f"Bullish ({bullish_count}/{total_count})"
        elif ratio <= 0.4:  # 2/5 or worse (equivalent to 1/6 would be ~0.17)
            return f"Bearish ({bullish_count}/{total_count})"
        else:
            return f"Neutral ({bullish_count}/{total_count})"

    def update_bias_labels(self, daily_bias, monthly_bias, quarterly_bias):
        """
        Update the bias labels in Box 1.

        Args:
            daily_bias: Daily bias result
            monthly_bias: Monthly bias result
            quarterly_bias: Quarterly bias result
        """
        self.bias_day_label.setText(f"Expected bias Day: {daily_bias}")
        self.bias_month_label.setText(f"Expected bias Month: {monthly_bias}")
        self.bias_quarter_label.setText(f"Expected bias Quarter: {quarterly_bias}")

        # Color code the labels
        for label, bias in [(self.bias_day_label, daily_bias),
                           (self.bias_month_label, monthly_bias),
                           (self.bias_quarter_label, quarterly_bias)]:
            if "Bullish" in bias:
                color = THEME_COLORS['bullish']
            elif "Bearish" in bias:
                color = THEME_COLORS['bearish']
            else:
                color = THEME_COLORS['text']

            label.setStyleSheet(f"color: {color}; font-size: 10px;")

    def populate_monthly_returns_table(self, data):
        """
        Populate the monthly returns table with percentage changes.

        Args:
            data: Historical price data
        """
        try:
            # Get unique years from data
            years = sorted(data.index.year.unique())
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

            if len(years) == 0:
                return

            # Set up table dimensions
            self.monthly_returns_table.setRowCount(len(years))
            self.monthly_returns_table.setColumnCount(12)  # 12 months

            # Set column headers (months)
            self.monthly_returns_table.setHorizontalHeaderLabels(months)

            # Set row headers (years)
            year_labels = [str(year) for year in years]
            self.monthly_returns_table.setVerticalHeaderLabels(year_labels)
            self.monthly_returns_table.verticalHeader().setVisible(True)

            # Calculate monthly returns for each year and month
            for row, year in enumerate(years):
                year_data = data[data.index.year == year]

                for col, month in enumerate(range(1, 13)):  # Months 1-12
                    month_data = year_data[year_data.index.month == month]

                    if len(month_data) >= 2:
                        # Get first and last day of the month
                        first_price = month_data['Close'].iloc[0]
                        last_price = month_data['Close'].iloc[-1]

                        # Calculate percentage change
                        pct_change = ((last_price - first_price) / first_price) * 100

                        # Create table item
                        item = QtWidgets.QTableWidgetItem(f"{pct_change:.1f}%")

                        # Set text color to default (no color coding)
                        item.setForeground(QtGui.QColor(THEME_COLORS['text']))

                        # Center align text
                        item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)

                        self.monthly_returns_table.setItem(row, col, item)
                    else:
                        # No data for this month
                        item = QtWidgets.QTableWidgetItem("-")
                        item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
                        item.setForeground(QtGui.QColor(THEME_COLORS['text']))
                        self.monthly_returns_table.setItem(row, col, item)

            # Resize columns to fit content
            self.monthly_returns_table.resizeColumnsToContents()

        except Exception as e:
            logger.error(f"Error populating monthly returns table: {str(e)}")

    def on_years_changed(self, value):
        """
        Handle changes to the years to load spinbox.

        Args:
            value: The new years value
        """
        logger.info(f"Years to load changed to: {value}")
        # Future functionality can be added here to reload data with new years value

    def get_years_to_load(self):
        """
        Get the current years to load value.

        Returns:
            int: The number of years to load
        """
        return self.years_spinbox.value()

    def set_years_to_load(self, years):
        """
        Set the years to load value.

        Args:
            years: The number of years to load
        """
        self.years_spinbox.setValue(years)

    def on_data_fetched_universal(self, symbol, data):
        """
        Handle data fetched from universal controls.
        
        Args:
            symbol: The symbol that was fetched
            data: The fetched data (pandas DataFrame)
        """
        # For now, just log that data was received
        # Future seasonality analysis functionality can be added here
        logger.info(f"Seasonality tab received data for {symbol}: {len(data) if data is not None else 0} rows")
        
    def apply_theme(self, theme_colors=None):
        """
        Apply theme colors to the tab.
        
        Args:
            theme_colors: Dictionary of theme colors (optional)
        """
        if theme_colors:
            self.chart_colors.update(theme_colors)
            
        # Update the background color
        background_color = self.chart_colors.get('background', THEME_COLORS['background'])
        self.setStyleSheet(f"background-color: {background_color};")
        self.black_widget.setStyleSheet(f"background-color: {background_color};")
        
    def get_data_from_data_tab(self):
        """
        Get data from the Data tab (placeholder for future functionality).
        
        Returns:
            pandas.DataFrame: The data from the Data tab, or None if not available
        """
        # Placeholder for future data retrieval functionality
        logger.info("Seasonality tab data retrieval not yet implemented")
        return None
        
    def refresh_data(self, *args):
        """
        Refresh the seasonality analysis with the latest data.
        
        This method can handle different signal signatures for compatibility.
        """
        # Placeholder for future data refresh functionality
        logger.info("Seasonality tab data refresh not yet implemented")
        
    def plot_seasonality_data(self, data):
        """
        Plot seasonality analysis (placeholder for future functionality).
        
        Args:
            data: pandas DataFrame with the data to analyze
        """
        # Placeholder for future seasonality plotting functionality
        logger.info("Seasonality plotting not yet implemented")
        
    def calculate_seasonal_patterns(self, data):
        """
        Calculate seasonal patterns in the data (placeholder for future functionality).
        
        Args:
            data: pandas DataFrame with the data to analyze
            
        Returns:
            dict: Dictionary containing seasonal analysis results
        """
        # Placeholder for future seasonal pattern calculation
        logger.info("Seasonal pattern calculation not yet implemented")
        return {}
